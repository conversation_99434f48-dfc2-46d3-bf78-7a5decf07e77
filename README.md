# 3D赛车小游戏

一个基于Three.js和Cannon-es的现代3D赛车游戏，遵循纯粹黑白美学设计。

## 项目特色

- **纯粹黑白美学**：坚持黑白灰色调，线条简洁，留白艺术
- **流畅3D体验**：60FPS流畅渲染，优化的性能表现
- **物理引擎**：真实的车辆物理模拟和碰撞检测
- **沉浸音效**：引擎声、轮胎摩擦声、环境音效
- **响应式设计**：支持桌面和移动设备

## 技术架构

- **3D引擎**：Three.js (最新版本)
- **物理引擎**：Cannon-es
- **音频系统**：Web Audio API
- **构建工具**：原生ES6模块，无需复杂构建流程

## 游戏功能

### 已实现功能
- [x] 3D场景与赛道系统
- [x] 赛车模型与控制
- [x] 物理引擎集成
- [x] 音效系统（合成音效）
- [x] UI界面（速度表、计时器）
- [x] 性能优化

### 控制方式
- **W/↑**：加速
- **S/↓**：刹车/倒车
- **A/←**：左转
- **D/→**：右转
- **空格**：手刹

## 文件结构

```
├── index.html          # 主页面
├── css/
│   └── style.css      # 样式文件
├── js/
│   ├── main.js        # 主程序入口
│   ├── game/
│   │   ├── Scene.js   # 3D场景管理
│   │   ├── Car.js     # 赛车类
│   │   ├── Track.js   # 赛道类
│   │   └── Physics.js # 物理系统
│   ├── ui/
│   │   └── UI.js      # 用户界面
│   └── audio/
│       └── AudioManager.js # 音频管理
├── assets/
│   ├── sounds/        # 音效文件
│   └── textures/      # 纹理文件
└── README.md
```

## 开发进度

- [x] 项目架构设计
- [x] 基础3D场景创建
- [x] 赛车模型实现
- [x] 物理系统集成
- [x] 音效系统添加
- [x] 性能优化完成

## 运行方式

1. 使用本地服务器运行：
   ```bash
   python3 -m http.server 8080
   ```
   或使用Live Server扩展

2. 打开浏览器访问 `http://localhost:8080`
3. 点击"开始游戏"按钮
4. 使用WASD或方向键控制赛车，空格键为手刹

## 游戏特色

### 视觉设计
- **纯粹黑白美学**：严格遵循黑白灰色调，拒绝彩色元素
- **线条艺术**：通过边框和分割线创造清晰的结构感
- **留白设计**：适当的留白创造呼吸感，避免视觉拥挤
- **等宽字体**：使用JetBrains Mono增强技术感

### 技术实现
- **合成音效**：使用Web Audio API动态生成引擎声、刹车声、碰撞声
- **物理模拟**：真实的车辆物理，包括转向、加速、刹车、惯性
- **相机跟随**：平滑的第三人称相机跟随系统
- **性能优化**：60FPS流畅渲染，支持移动设备

## 性能要求

- 现代浏览器（支持WebGL 2.0）
- 推荐Chrome/Firefox/Safari最新版本
- 移动设备需支持WebGL

## 故障排除

如果遇到问题，请尝试以下解决方案：

1. **游戏无法加载**：
   - 确保使用本地服务器运行（不能直接打开HTML文件）
   - 检查浏览器控制台是否有错误信息
   - 尝试使用Chrome或Firefox最新版本

2. **音效无法播放**：
   - 游戏使用Web Audio API合成音效，需要用户交互后才能播放
   - 点击"开始游戏"按钮后音效会自动启用

3. **性能问题**：
   - 降低浏览器窗口大小
   - 关闭其他占用GPU的应用程序
   - 确保显卡驱动程序是最新版本

## 技术细节

- **渲染引擎**：Three.js WebGL渲染
- **物理模拟**：自定义简化物理引擎
- **音频处理**：Web Audio API实时合成
- **响应式设计**：支持桌面和移动设备
- **性能优化**：60FPS目标帧率

## 开发说明

项目采用模块化架构，易于扩展和维护：
- `Scene.js` - 3D场景管理
- `Car.js` - 车辆物理和渲染
- `Track.js` - 赛道生成
- `SimplePhysics.js` - 简化物理引擎
- `UI.js` - 用户界面管理
- `AudioManager.js` - 音频系统

---

*遵循纯粹黑白美学，功能与美学并重的设计理念*

**🎮 现在就开始体验这个独特的3D赛车游戏吧！**
