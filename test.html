<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D赛车小游戏 - 测试</title>
    <link rel="stylesheet" href="css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 游戏容器 -->
    <div id="game-container">
        <!-- 3D渲染画布 -->
        <canvas id="game-canvas"></canvas>
        
        <!-- 游戏UI覆盖层 -->
        <div id="game-ui">
            <!-- 顶部信息栏 -->
            <div class="ui-top">
                <div class="info-panel">
                    <div class="speed-display">
                        <span class="label">速度</span>
                        <span class="value" id="speed-value">0</span>
                        <span class="unit">km/h</span>
                    </div>
                    <div class="time-display">
                        <span class="label">时间</span>
                        <span class="value" id="time-value">00:00</span>
                    </div>
                </div>
            </div>
            
            <!-- 开始界面 -->
            <div id="start-screen" class="screen">
                <div class="screen-content">
                    <h1 class="game-title">3D赛车</h1>
                    <p class="game-subtitle">纯粹黑白美学 · 极致驾驶体验</p>
                    <button id="start-button" class="game-button">开始游戏</button>
                    <div class="game-info">
                        <p>使用WASD或方向键控制赛车</p>
                        <p>空格键为手刹</p>
                    </div>
                </div>
            </div>
            
            <!-- 暂停界面 -->
            <div id="pause-screen" class="screen hidden">
                <div class="screen-content">
                    <h2 class="screen-title">游戏暂停</h2>
                    <button id="resume-button" class="game-button">继续游戏</button>
                    <button id="restart-button" class="game-button">重新开始</button>
                </div>
            </div>
            
            <!-- 加载界面 -->
            <div id="loading-screen" class="screen hidden">
                <div class="screen-content">
                    <div class="loading-spinner"></div>
                    <p class="loading-text">加载中...</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 简化的脚本 -->
    <script type="module">
        import { UI } from './js/ui/UI.js';
        
        console.log('开始测试UI系统...');
        
        window.addEventListener('DOMContentLoaded', () => {
            console.log('DOM加载完成');
            
            try {
                const ui = new UI();
                console.log('UI创建成功');
                
                // 测试UI方法
                document.getElementById('start-button').addEventListener('click', () => {
                    console.log('开始按钮被点击');
                    ui.showGame();
                    ui.showSuccess('游戏开始！');
                });
                
                document.getElementById('resume-button').addEventListener('click', () => {
                    console.log('继续按钮被点击');
                    ui.showGame();
                });
                
                document.getElementById('restart-button').addEventListener('click', () => {
                    console.log('重启按钮被点击');
                    ui.showStart();
                });
                
                // 测试键盘事件
                document.addEventListener('keydown', (event) => {
                    if (event.code === 'Escape') {
                        ui.showPause();
                    }
                });
                
                console.log('测试页面初始化完成');
                
            } catch (error) {
                console.error('测试失败:', error);
            }
        });
    </script>
</body>
</html>
