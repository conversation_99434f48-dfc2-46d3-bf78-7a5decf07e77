/**
 * UI管理类
 * 负责游戏界面的显示和交互
 */

export class UI {
    constructor() {
        console.log('初始化UI系统...');

        // UI元素引用
        this.speedValue = document.getElementById('speed-value');
        this.timeValue = document.getElementById('time-value');

        // 屏幕元素
        this.startScreen = document.getElementById('start-screen');
        this.pauseScreen = document.getElementById('pause-screen');
        this.loadingScreen = document.getElementById('loading-screen');

        // 按钮元素
        this.startButton = document.getElementById('start-button');
        this.resumeButton = document.getElementById('resume-button');
        this.restartButton = document.getElementById('restart-button');

        // 检查关键元素是否存在
        if (!this.startScreen) {
            console.error('未找到开始界面元素');
        }
        if (!this.startButton) {
            console.error('未找到开始按钮元素');
        }

        // 状态
        this.currentScreen = 'start';

        console.log('UI系统初始化完成');
    }
    
    // 显示开始界面
    showStart() {
        this.hideAll();
        this.startScreen.classList.remove('hidden');
        this.currentScreen = 'start';
    }
    
    // 显示游戏界面
    showGame() {
        this.hideAll();
        this.currentScreen = 'game';
    }
    
    // 显示暂停界面
    showPause() {
        this.pauseScreen.classList.remove('hidden');
        this.currentScreen = 'pause';
    }
    
    // 隐藏暂停界面
    hidePause() {
        this.pauseScreen.classList.add('hidden');
        this.currentScreen = 'game';
    }
    
    // 显示加载界面
    showLoading() {
        this.hideAll();
        this.loadingScreen.classList.remove('hidden');
        this.currentScreen = 'loading';
    }
    
    // 隐藏加载界面
    hideLoading() {
        this.loadingScreen.classList.add('hidden');
    }
    
    // 隐藏所有界面
    hideAll() {
        this.startScreen.classList.add('hidden');
        this.pauseScreen.classList.add('hidden');
        this.loadingScreen.classList.add('hidden');
    }
    
    // 更新速度显示
    updateSpeed(speed) {
        if (this.speedValue) {
            this.speedValue.textContent = speed.toString();
        }
    }
    
    // 更新时间显示
    updateTime(timeInSeconds) {
        if (this.timeValue) {
            const minutes = Math.floor(timeInSeconds / 60);
            const seconds = Math.floor(timeInSeconds % 60);
            const formattedTime = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            this.timeValue.textContent = formattedTime;
        }
    }
    
    // 显示错误信息
    showError(message) {
        // 创建错误提示元素
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            color: #ffffff;
            padding: 20px 30px;
            border: 1px solid #ff4444;
            font-family: 'JetBrains Mono', monospace;
            font-size: 14px;
            text-align: center;
            z-index: 1000;
            max-width: 400px;
        `;
        errorDiv.textContent = message;
        
        document.body.appendChild(errorDiv);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.parentNode.removeChild(errorDiv);
            }
        }, 3000);
    }
    
    // 显示成功信息
    showSuccess(message) {
        const successDiv = document.createElement('div');
        successDiv.className = 'success-message';
        successDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.9);
            color: #ffffff;
            padding: 15px 20px;
            border: 1px solid #44ff44;
            font-family: 'JetBrains Mono', monospace;
            font-size: 12px;
            z-index: 1000;
            max-width: 300px;
        `;
        successDiv.textContent = message;
        
        document.body.appendChild(successDiv);
        
        // 2秒后自动移除
        setTimeout(() => {
            if (successDiv.parentNode) {
                successDiv.parentNode.removeChild(successDiv);
            }
        }, 2000);
    }
    
    // 显示提示信息
    showHint(message, duration = 3000) {
        const hintDiv = document.createElement('div');
        hintDiv.className = 'hint-message';
        hintDiv.style.cssText = `
            position: fixed;
            bottom: 100px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: #ffffff;
            padding: 10px 20px;
            border: 1px solid #ffffff;
            font-family: 'JetBrains Mono', monospace;
            font-size: 12px;
            text-align: center;
            z-index: 1000;
            opacity: 0.8;
        `;
        hintDiv.textContent = message;
        
        document.body.appendChild(hintDiv);
        
        setTimeout(() => {
            if (hintDiv.parentNode) {
                hintDiv.parentNode.removeChild(hintDiv);
            }
        }, duration);
    }
    
    // 创建进度条
    createProgressBar(container, initialValue = 0) {
        const progressContainer = document.createElement('div');
        progressContainer.style.cssText = `
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid #ffffff;
            margin: 10px 0;
        `;
        
        const progressBar = document.createElement('div');
        progressBar.style.cssText = `
            width: ${initialValue}%;
            height: 100%;
            background: #ffffff;
            transition: width 0.3s ease;
        `;
        
        progressContainer.appendChild(progressBar);
        container.appendChild(progressContainer);
        
        return {
            update: (value) => {
                progressBar.style.width = `${Math.max(0, Math.min(100, value))}%`;
            },
            remove: () => {
                if (progressContainer.parentNode) {
                    progressContainer.parentNode.removeChild(progressContainer);
                }
            }
        };
    }
    
    // 添加键盘提示动画
    animateKeyHint(keyElement) {
        if (!keyElement) return;
        
        keyElement.style.transform = 'scale(1.1)';
        keyElement.style.background = '#ffffff';
        keyElement.style.color = '#000000';
        
        setTimeout(() => {
            keyElement.style.transform = 'scale(1)';
            keyElement.style.background = '#000000';
            keyElement.style.color = '#ffffff';
        }, 150);
    }
    
    // 更新控制提示状态
    updateControlHints(keys) {
        const controlElements = {
            forward: document.querySelector('.control-group:nth-child(1) .key'),
            backward: document.querySelector('.control-group:nth-child(2) .key'),
            left: document.querySelector('.control-group:nth-child(3) .key'),
            right: document.querySelector('.control-group:nth-child(4) .key'),
            brake: document.querySelector('.control-group:nth-child(5) .key')
        };
        
        // 重置所有按键样式
        Object.values(controlElements).forEach(element => {
            if (element) {
                element.style.background = '#ffffff';
                element.style.color = '#000000';
                element.style.transform = 'scale(1)';
            }
        });
        
        // 高亮激活的按键
        if (keys.forward && controlElements.forward) {
            this.animateKeyHint(controlElements.forward);
        }
        if (keys.backward && controlElements.backward) {
            this.animateKeyHint(controlElements.backward);
        }
        if (keys.left && controlElements.left) {
            this.animateKeyHint(controlElements.left);
        }
        if (keys.right && controlElements.right) {
            this.animateKeyHint(controlElements.right);
        }
        if (keys.brake && controlElements.brake) {
            this.animateKeyHint(controlElements.brake);
        }
    }
    
    // 获取当前屏幕状态
    getCurrentScreen() {
        return this.currentScreen;
    }
    
    // 检查是否在游戏中
    isInGame() {
        return this.currentScreen === 'game';
    }
    
    // 销毁UI
    dispose() {
        // 移除所有动态创建的元素
        const dynamicElements = document.querySelectorAll('.error-message, .success-message, .hint-message');
        dynamicElements.forEach(element => {
            if (element.parentNode) {
                element.parentNode.removeChild(element);
            }
        });
    }
}
