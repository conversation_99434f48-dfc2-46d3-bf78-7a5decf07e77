/**
 * 3D赛车游戏主程序
 * 遵循纯粹黑白美学设计理念
 */

import { GameScene } from './game/Scene.js';
import { Car } from './game/Car.js';
import { Track } from './game/Track.js';
import { SimplePhysics as Physics } from './game/SimplePhysics.js';
import { UI } from './ui/UI.js';
import { AudioManager } from './audio/AudioManager.js';

class RacingGame {
    constructor() {
        this.canvas = document.getElementById('game-canvas');
        this.isRunning = false;
        this.isPaused = false;
        this.gameTime = 0;
        this.lastTime = 0;
        
        // 游戏组件
        this.scene = null;
        this.car = null;
        this.track = null;
        this.physics = null;
        this.ui = null;
        this.audio = null;
        
        // 控制状态
        this.keys = {
            forward: false,
            backward: false,
            left: false,
            right: false,
            brake: false
        };
        
        this.init();
    }
    
    async init() {
        try {
            console.log('开始初始化游戏...');

            // 初始化UI系统
            this.ui = new UI();
            this.ui.showLoading();

            console.log('UI初始化完成，开始初始化其他组件...');

            // 初始化音频系统
            this.audio = new AudioManager();
            await this.audio.init();
            console.log('音频系统初始化完成');

            // 初始化3D场景
            this.scene = new GameScene(this.canvas);
            await this.scene.init();
            console.log('3D场景初始化完成');

            // 初始化物理引擎
            this.physics = new Physics();
            await this.physics.init();
            console.log('物理引擎初始化完成');

            // 创建赛道
            this.track = new Track(this.scene, this.physics);
            await this.track.create();
            console.log('赛道创建完成');

            // 创建赛车
            this.car = new Car(this.scene, this.physics);
            await this.car.create();
            console.log('赛车创建完成');

            // 设置相机跟随
            this.scene.setCameraTarget(this.car.mesh);

            // 绑定事件
            this.bindEvents();

            // 隐藏加载界面，显示开始界面
            this.ui.hideLoading();
            this.ui.showStart();

            console.log('游戏初始化完成');

        } catch (error) {
            console.error('游戏初始化失败:', error);
            if (this.ui && this.ui.showError) {
                this.ui.showError('游戏加载失败，请刷新页面重试');
            } else {
                alert('游戏加载失败，请刷新页面重试');
            }
        }
    }
    
    bindEvents() {
        // 键盘事件
        document.addEventListener('keydown', (event) => this.onKeyDown(event));
        document.addEventListener('keyup', (event) => this.onKeyUp(event));
        
        // UI事件
        document.getElementById('start-button').addEventListener('click', () => this.startGame());
        document.getElementById('resume-button').addEventListener('click', () => this.resumeGame());
        document.getElementById('restart-button').addEventListener('click', () => this.restartGame());
        
        // 窗口事件
        window.addEventListener('resize', () => this.onWindowResize());
        window.addEventListener('blur', () => this.pauseGame());
        
        // 防止右键菜单
        this.canvas.addEventListener('contextmenu', (event) => event.preventDefault());
    }
    
    onKeyDown(event) {
        if (!this.isRunning || this.isPaused) return;
        
        switch (event.code) {
            case 'KeyW':
            case 'ArrowUp':
                this.keys.forward = true;
                break;
            case 'KeyS':
            case 'ArrowDown':
                this.keys.backward = true;
                break;
            case 'KeyA':
            case 'ArrowLeft':
                this.keys.left = true;
                break;
            case 'KeyD':
            case 'ArrowRight':
                this.keys.right = true;
                break;
            case 'Space':
                this.keys.brake = true;
                event.preventDefault();
                break;
            case 'Escape':
                this.pauseGame();
                break;
        }
    }
    
    onKeyUp(event) {
        switch (event.code) {
            case 'KeyW':
            case 'ArrowUp':
                this.keys.forward = false;
                break;
            case 'KeyS':
            case 'ArrowDown':
                this.keys.backward = false;
                break;
            case 'KeyA':
            case 'ArrowLeft':
                this.keys.left = false;
                break;
            case 'KeyD':
            case 'ArrowRight':
                this.keys.right = false;
                break;
            case 'Space':
                this.keys.brake = false;
                break;
        }
    }
    
    onWindowResize() {
        if (this.scene) {
            this.scene.onWindowResize();
        }
    }
    
    startGame() {
        this.isRunning = true;
        this.isPaused = false;
        this.gameTime = 0;
        this.lastTime = performance.now();

        this.ui.showGame();
        
        // 开始游戏循环
        this.gameLoop();
        
        // 播放引擎声音
        this.audio.playEngine();
        
        console.log('游戏开始');
    }
    
    pauseGame() {
        if (!this.isRunning) return;
        
        this.isPaused = true;
        this.ui.showPause();
        this.audio.pauseEngine();
        
        console.log('游戏暂停');
    }
    
    resumeGame() {
        if (!this.isRunning) return;
        
        this.isPaused = false;
        this.lastTime = performance.now();
        
        this.ui.hidePause();
        this.gameLoop();
        this.audio.resumeEngine();
        
        console.log('游戏继续');
    }
    
    restartGame() {
        this.isRunning = false;
        this.isPaused = false;
        
        // 重置游戏状态
        if (this.car) {
            this.car.reset();
        }
        
        this.gameTime = 0;
        this.ui.hideAll();
        this.ui.showStart();
        this.audio.stopAll();
        
        console.log('游戏重启');
    }
    
    gameLoop() {
        if (!this.isRunning || this.isPaused) return;
        
        const currentTime = performance.now();
        const deltaTime = (currentTime - this.lastTime) / 1000;
        this.lastTime = currentTime;
        this.gameTime += deltaTime;
        
        // 更新物理引擎
        this.physics.step(deltaTime);
        
        // 更新赛车
        this.car.update(deltaTime, this.keys);
        
        // 更新场景
        this.scene.update(deltaTime);
        
        // 更新UI
        this.ui.updateSpeed(this.car.getSpeed());
        this.ui.updateTime(this.gameTime);
        
        // 更新音频
        this.audio.updateEngine(this.car.getSpeed(), this.keys.forward);
        
        // 渲染场景
        this.scene.render();
        
        // 继续游戏循环
        requestAnimationFrame(() => this.gameLoop());
    }
}

// 启动游戏
window.addEventListener('DOMContentLoaded', () => {
    console.log('DOM加载完成，开始初始化游戏...');
    try {
        new RacingGame();
    } catch (error) {
        console.error('游戏初始化失败:', error);
    }
});
