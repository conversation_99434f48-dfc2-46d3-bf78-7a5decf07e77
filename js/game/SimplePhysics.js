/**
 * 简化物理引擎
 * 不依赖外部库，实现基本的物理模拟
 */

export class SimplePhysics {
    constructor() {
        this.gravity = -9.82;
        this.bodies = [];
        this.timeStep = 1 / 60;
    }
    
    async init() {
        console.log('简化物理引擎初始化完成');
    }
    
    // 创建简单的刚体
    createBody(options = {}) {
        const body = {
            position: { x: 0, y: 0, z: 0 },
            velocity: { x: 0, y: 0, z: 0 },
            acceleration: { x: 0, y: 0, z: 0 },
            rotation: { x: 0, y: 0, z: 0 },
            angularVelocity: { x: 0, y: 0, z: 0 },
            mass: options.mass || 1,
            friction: options.friction || 0.1,
            restitution: options.restitution || 0.3,
            isStatic: options.mass === 0,
            ...options
        };
        
        this.bodies.push(body);
        return body;
    }
    
    // 创建盒子形状的刚体
    createBox(size, position, mass = 1, material = null) {
        return this.createBody({
            position: { ...position },
            mass: mass,
            size: { ...size },
            shape: 'box',
            material: material
        });
    }
    
    // 创建球形刚体
    createSphere(radius, position, mass = 1, material = null) {
        return this.createBody({
            position: { ...position },
            mass: mass,
            radius: radius,
            shape: 'sphere',
            material: material
        });
    }
    
    // 创建圆柱体刚体
    createCylinder(radiusTop, radiusBottom, height, position, mass = 1, material = null) {
        return this.createBody({
            position: { ...position },
            mass: mass,
            radiusTop: radiusTop,
            radiusBottom: radiusBottom,
            height: height,
            shape: 'cylinder',
            material: material
        });
    }
    
    // 添加力到刚体
    applyForce(body, force) {
        if (body.isStatic) return;
        
        body.acceleration.x += force.x / body.mass;
        body.acceleration.y += force.y / body.mass;
        body.acceleration.z += force.z / body.mass;
    }
    
    // 添加冲量到刚体
    applyImpulse(body, impulse) {
        if (body.isStatic) return;
        
        body.velocity.x += impulse.x / body.mass;
        body.velocity.y += impulse.y / body.mass;
        body.velocity.z += impulse.z / body.mass;
    }
    
    // 更新物理模拟
    step(deltaTime) {
        const dt = Math.min(deltaTime, this.timeStep);
        
        this.bodies.forEach(body => {
            if (body.isStatic) return;
            
            // 应用重力
            body.acceleration.y += this.gravity;
            
            // 更新速度
            body.velocity.x += body.acceleration.x * dt;
            body.velocity.y += body.acceleration.y * dt;
            body.velocity.z += body.acceleration.z * dt;
            
            // 应用阻尼
            const damping = Math.pow(1 - body.friction, dt);
            body.velocity.x *= damping;
            body.velocity.z *= damping;
            
            // 更新位置
            body.position.x += body.velocity.x * dt;
            body.position.y += body.velocity.y * dt;
            body.position.z += body.velocity.z * dt;
            
            // 地面碰撞检测
            if (body.position.y < 0) {
                body.position.y = 0;
                body.velocity.y = -body.velocity.y * body.restitution;
                
                // 如果速度很小，停止弹跳
                if (Math.abs(body.velocity.y) < 0.1) {
                    body.velocity.y = 0;
                }
            }
            
            // 重置加速度
            body.acceleration.x = 0;
            body.acceleration.y = 0;
            body.acceleration.z = 0;
        });
    }
    
    // 添加刚体
    addBody(body) {
        if (!this.bodies.includes(body)) {
            this.bodies.push(body);
        }
    }
    
    // 移除刚体
    removeBody(body) {
        const index = this.bodies.indexOf(body);
        if (index > -1) {
            this.bodies.splice(index, 1);
        }
    }
    
    // 获取材质（兼容接口）
    getMaterial(name) {
        return {
            name: name,
            friction: name === 'wheel' ? 1.0 : 0.5,
            restitution: name === 'wheel' ? 0.1 : 0.3
        };
    }
    
    // 创建约束（简化版本）
    createConstraint(bodyA, bodyB, options = {}) {
        // 简化的约束系统，主要用于轮子连接
        return {
            bodyA: bodyA,
            bodyB: bodyB,
            type: options.type,
            options: options
        };
    }
    
    // 添加约束
    addConstraint(constraint) {
        // 简化实现，暂时不处理约束
        console.log('约束已添加（简化版本）');
    }
    
    // 移除约束
    removeConstraint(constraint) {
        // 简化实现
        console.log('约束已移除（简化版本）');
    }
    
    // 射线检测
    raycast(from, to, options = {}) {
        // 简化的射线检测，只检测地面
        const direction = {
            x: to.x - from.x,
            y: to.y - from.y,
            z: to.z - from.z
        };
        
        const length = Math.sqrt(direction.x * direction.x + direction.y * direction.y + direction.z * direction.z);
        
        // 检查是否与地面相交
        if (from.y > 0 && to.y <= 0) {
            const t = -from.y / direction.y;
            const hitPoint = {
                x: from.x + direction.x * t,
                y: 0,
                z: from.z + direction.z * t
            };
            
            return {
                hasHit: true,
                body: null,
                shape: null,
                hitPoint: hitPoint,
                hitNormal: { x: 0, y: 1, z: 0 },
                distance: t * length
            };
        }
        
        return {
            hasHit: false,
            body: null,
            shape: null,
            hitPoint: null,
            hitNormal: null,
            distance: Infinity
        };
    }
    
    // 设置重力
    setGravity(x, y, z) {
        this.gravity = y;
    }
    
    // 获取世界对象（兼容接口）
    getWorld() {
        return this;
    }
    
    // 销毁物理世界
    dispose() {
        this.bodies = [];
    }
}
