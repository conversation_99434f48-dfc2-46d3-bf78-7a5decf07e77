/**
 * 赛车类
 * 创建3D赛车模型并实现物理控制
 */

import * as THREE from 'three';

export class Car {
    constructor(scene, physics) {
        this.scene = scene;
        this.physics = physics;
        
        // 3D模型组件
        this.mesh = null;
        this.wheels = [];
        
        // 物理组件
        this.body = null;
        this.wheelBodies = [];
        this.wheelConstraints = [];
        
        // 车辆参数
        this.carSize = { x: 2, y: 0.8, z: 4 };
        this.wheelRadius = 0.4;
        this.wheelWidth = 0.3;
        this.wheelOffset = { x: 1.2, y: -0.2, z: 1.5 };
        
        // 控制参数
        this.maxSteerValue = 0.5;
        this.maxForce = 1500;
        this.brakeForce = 1000;
        this.maxSpeed = 50; // km/h
        
        // 当前状态
        this.currentSpeed = 0;
        this.currentSteer = 0;
        this.isAccelerating = false;
        this.isBraking = false;
    }
    
    async create() {
        // 创建车身
        this.createCarBody();
        
        // 创建轮子
        this.createWheels();
        
        // 设置初始位置
        this.reset();
        
        console.log('赛车创建完成');
    }
    
    createCarBody() {
        // 创建车身几何体
        const carGroup = new THREE.Group();
        
        // 主车身
        const bodyGeometry = new THREE.BoxGeometry(
            this.carSize.x,
            this.carSize.y,
            this.carSize.z
        );
        
        const bodyMaterial = new THREE.MeshLambertMaterial({
            color: 0x333333,
            transparent: false
        });
        
        const bodyMesh = new THREE.Mesh(bodyGeometry, bodyMaterial);
        bodyMesh.castShadow = true;
        bodyMesh.receiveShadow = true;
        carGroup.add(bodyMesh);
        
        // 车顶
        const roofGeometry = new THREE.BoxGeometry(
            this.carSize.x * 0.8,
            this.carSize.y * 0.4,
            this.carSize.z * 0.6
        );
        
        const roofMaterial = new THREE.MeshLambertMaterial({
            color: 0x666666,
            transparent: false
        });
        
        const roofMesh = new THREE.Mesh(roofGeometry, roofMaterial);
        roofMesh.position.y = this.carSize.y * 0.6;
        roofMesh.castShadow = true;
        carGroup.add(roofMesh);
        
        // 前保险杠
        const bumperGeometry = new THREE.BoxGeometry(
            this.carSize.x * 0.9,
            this.carSize.y * 0.3,
            0.2
        );
        
        const bumperMaterial = new THREE.MeshLambertMaterial({
            color: 0xffffff,
            transparent: false
        });
        
        const frontBumper = new THREE.Mesh(bumperGeometry, bumperMaterial);
        frontBumper.position.z = this.carSize.z / 2 + 0.1;
        frontBumper.position.y = -this.carSize.y * 0.2;
        frontBumper.castShadow = true;
        carGroup.add(frontBumper);
        
        // 后保险杠
        const rearBumper = new THREE.Mesh(bumperGeometry, bumperMaterial);
        rearBumper.position.z = -this.carSize.z / 2 - 0.1;
        rearBumper.position.y = -this.carSize.y * 0.2;
        rearBumper.castShadow = true;
        carGroup.add(rearBumper);
        
        // 添加车灯
        this.addLights(carGroup);
        
        this.mesh = carGroup;
        this.scene.add(this.mesh);
        
        // 创建物理车身
        this.body = this.physics.createBox(
            this.carSize,
            { x: 0, y: 2, z: 0 },
            500, // 车身质量
            this.physics.getMaterial('car')
        );
        
        // 设置车身的线性和角阻尼
        this.body.linearDamping = 0.1;
        this.body.angularDamping = 0.1;
    }
    
    addLights(carGroup) {
        // 前大灯
        const headlightGeometry = new THREE.SphereGeometry(0.15, 8, 6);
        const headlightMaterial = new THREE.MeshLambertMaterial({
            color: 0xffffcc,
            emissive: 0x444422
        });
        
        const leftHeadlight = new THREE.Mesh(headlightGeometry, headlightMaterial);
        leftHeadlight.position.set(-0.6, 0, this.carSize.z / 2 - 0.2);
        carGroup.add(leftHeadlight);
        
        const rightHeadlight = new THREE.Mesh(headlightGeometry, headlightMaterial);
        rightHeadlight.position.set(0.6, 0, this.carSize.z / 2 - 0.2);
        carGroup.add(rightHeadlight);
        
        // 尾灯
        const taillightGeometry = new THREE.SphereGeometry(0.1, 8, 6);
        const taillightMaterial = new THREE.MeshLambertMaterial({
            color: 0xff4444,
            emissive: 0x220000
        });
        
        const leftTaillight = new THREE.Mesh(taillightGeometry, taillightMaterial);
        leftTaillight.position.set(-0.6, 0, -this.carSize.z / 2 + 0.2);
        carGroup.add(leftTaillight);
        
        const rightTaillight = new THREE.Mesh(taillightGeometry, taillightMaterial);
        rightTaillight.position.set(0.6, 0, -this.carSize.z / 2 + 0.2);
        carGroup.add(rightTaillight);
    }
    
    createWheels() {
        const wheelPositions = [
            { x: -this.wheelOffset.x, y: this.wheelOffset.y, z: this.wheelOffset.z },   // 左前
            { x: this.wheelOffset.x, y: this.wheelOffset.y, z: this.wheelOffset.z },    // 右前
            { x: -this.wheelOffset.x, y: this.wheelOffset.y, z: -this.wheelOffset.z },  // 左后
            { x: this.wheelOffset.x, y: this.wheelOffset.y, z: -this.wheelOffset.z }    // 右后
        ];
        
        wheelPositions.forEach((pos, index) => {
            // 创建轮子3D模型
            const wheelGeometry = new THREE.CylinderGeometry(
                this.wheelRadius,
                this.wheelRadius,
                this.wheelWidth,
                16
            );
            
            const wheelMaterial = new THREE.MeshLambertMaterial({
                color: 0x222222,
                transparent: false
            });
            
            const wheelMesh = new THREE.Mesh(wheelGeometry, wheelMaterial);
            wheelMesh.rotation.z = Math.PI / 2; // 旋转轮子方向
            wheelMesh.castShadow = true;
            wheelMesh.receiveShadow = true;
            
            // 添加轮毂
            const rimGeometry = new THREE.CylinderGeometry(
                this.wheelRadius * 0.7,
                this.wheelRadius * 0.7,
                this.wheelWidth * 0.8,
                8
            );
            
            const rimMaterial = new THREE.MeshLambertMaterial({
                color: 0xcccccc,
                transparent: false
            });
            
            const rimMesh = new THREE.Mesh(rimGeometry, rimMaterial);
            rimMesh.rotation.z = Math.PI / 2;
            wheelMesh.add(rimMesh);
            
            this.wheels.push(wheelMesh);
            this.scene.add(wheelMesh);
            
            // 创建轮子物理体
            const wheelBody = this.physics.createCylinder(
                this.wheelRadius,
                this.wheelRadius,
                this.wheelWidth,
                { x: pos.x, y: 2 + pos.y, z: pos.z },
                20, // 轮子质量
                this.physics.getMaterial('wheel')
            );
            
            wheelBody.linearDamping = 0.4;
            wheelBody.angularDamping = 0.4;
            
            this.wheelBodies.push(wheelBody);
            
            // 创建轮子约束
            const constraint = this.physics.createConstraint(this.body, wheelBody, {
                type: 'pointToPoint',
                pivotA: pos,
                pivotB: { x: 0, y: 0, z: 0 }
            });
            
            this.wheelConstraints.push(constraint);
        });
    }

    update(deltaTime, keys) {
        // 更新物理状态
        this.updatePhysics(deltaTime, keys);

        // 同步3D模型位置
        this.syncMeshWithPhysics();

        // 更新速度
        this.updateSpeed();
    }

    updatePhysics(deltaTime, keys) {
        // 转向控制
        let steerInput = 0;
        if (keys.left) steerInput += 1;
        if (keys.right) steerInput -= 1;

        // 平滑转向
        const steerSpeed = 3.0;
        this.currentSteer = THREE.MathUtils.lerp(
            this.currentSteer,
            steerInput * this.maxSteerValue,
            steerSpeed * deltaTime
        );

        // 加速和刹车
        let forceInput = 0;
        if (keys.forward) {
            forceInput = 1;
            this.isAccelerating = true;
        } else if (keys.backward) {
            forceInput = -0.5; // 倒车力度较小
            this.isAccelerating = false;
        } else {
            this.isAccelerating = false;
        }

        // 手刹
        this.isBraking = keys.brake;

        // 应用力到轮子
        this.applyForces(forceInput, this.currentSteer);

        // 应用空气阻力
        this.applyAirResistance();
    }

    applyForces(forceInput, steerValue) {
        const force = forceInput * this.maxForce;

        // 前轮（索引0和1）负责转向和部分驱动
        // 后轮（索引2和3）负责主要驱动

        this.wheelBodies.forEach((wheelBody, index) => {
            const isFrontWheel = index < 2;
            const isLeftWheel = index % 2 === 0;

            // 计算轮子的局部坐标系
            const wheelForward = new THREE.Vector3(0, 0, 1);
            const wheelRight = new THREE.Vector3(1, 0, 0);

            if (isFrontWheel) {
                // 前轮转向
                wheelForward.applyAxisAngle(new THREE.Vector3(0, 1, 0), steerValue);
                wheelRight.applyAxisAngle(new THREE.Vector3(0, 1, 0), steerValue);
            }

            // 转换到世界坐标系
            const worldForward = wheelForward.clone();
            worldForward.applyQuaternion(this.body.quaternion);

            // 应用驱动力
            if (Math.abs(force) > 0) {
                const driveForce = force * (isFrontWheel ? 0.3 : 0.7); // 后轮驱动为主
                wheelBody.force.x += worldForward.x * driveForce;
                wheelBody.force.z += worldForward.z * driveForce;
            }

            // 应用刹车力
            if (this.isBraking) {
                const velocity = wheelBody.velocity;
                wheelBody.force.x -= velocity.x * this.brakeForce;
                wheelBody.force.z -= velocity.z * this.brakeForce;
            }

            // 侧向摩擦力（防止侧滑）
            const worldRight = wheelRight.clone();
            worldRight.applyQuaternion(this.body.quaternion);

            const lateralVelocity = new THREE.Vector3(
                wheelBody.velocity.x,
                0,
                wheelBody.velocity.z
            ).dot(worldRight);

            const lateralForce = -lateralVelocity * 1000;
            wheelBody.force.x += worldRight.x * lateralForce;
            wheelBody.force.z += worldRight.z * lateralForce;
        });
    }

    applyAirResistance() {
        // 空气阻力
        const velocity = this.body.velocity;
        const speed = Math.sqrt(velocity.x * velocity.x + velocity.z * velocity.z);
        const dragCoefficient = 0.3;
        const dragForce = speed * speed * dragCoefficient;

        if (speed > 0.1) {
            const dragDirection = new THREE.Vector3(-velocity.x, 0, -velocity.z).normalize();
            this.body.force.x += dragDirection.x * dragForce;
            this.body.force.z += dragDirection.z * dragForce;
        }
    }

    syncMeshWithPhysics() {
        // 同步车身位置和旋转
        this.mesh.position.copy(this.body.position);
        this.mesh.quaternion.copy(this.body.quaternion);

        // 同步轮子位置和旋转
        this.wheels.forEach((wheelMesh, index) => {
            const wheelBody = this.wheelBodies[index];
            wheelMesh.position.copy(wheelBody.position);
            wheelMesh.quaternion.copy(wheelBody.quaternion);

            // 保持轮子的正确方向
            wheelMesh.rotateZ(Math.PI / 2);
        });
    }

    updateSpeed() {
        const velocity = this.body.velocity;
        const speedMs = Math.sqrt(velocity.x * velocity.x + velocity.z * velocity.z);
        this.currentSpeed = speedMs * 3.6; // 转换为 km/h
    }

    getSpeed() {
        return Math.round(this.currentSpeed);
    }

    getPosition() {
        return this.body.position;
    }

    reset() {
        // 重置到起始位置
        const startPos = { x: 0, y: 2, z: -90 };

        // 重置车身
        this.body.position.set(startPos.x, startPos.y, startPos.z);
        this.body.quaternion.set(0, 0, 0, 1);
        this.body.velocity.set(0, 0, 0);
        this.body.angularVelocity.set(0, 0, 0);

        // 重置轮子
        this.wheelBodies.forEach((wheelBody, index) => {
            const wheelPos = [
                { x: startPos.x - this.wheelOffset.x, y: startPos.y + this.wheelOffset.y, z: startPos.z + this.wheelOffset.z },
                { x: startPos.x + this.wheelOffset.x, y: startPos.y + this.wheelOffset.y, z: startPos.z + this.wheelOffset.z },
                { x: startPos.x - this.wheelOffset.x, y: startPos.y + this.wheelOffset.y, z: startPos.z - this.wheelOffset.z },
                { x: startPos.x + this.wheelOffset.x, y: startPos.y + this.wheelOffset.y, z: startPos.z - this.wheelOffset.z }
            ][index];

            wheelBody.position.set(wheelPos.x, wheelPos.y, wheelPos.z);
            wheelBody.quaternion.set(0, 0, 0, 1);
            wheelBody.velocity.set(0, 0, 0);
            wheelBody.angularVelocity.set(0, 0, 0);
        });

        // 重置状态
        this.currentSpeed = 0;
        this.currentSteer = 0;
        this.isAccelerating = false;
        this.isBraking = false;
    }

    dispose() {
        // 清理3D模型
        if (this.mesh) {
            this.scene.remove(this.mesh);
        }

        this.wheels.forEach(wheel => {
            this.scene.remove(wheel);
            wheel.geometry.dispose();
            wheel.material.dispose();
        });

        // 清理物理体
        if (this.body) {
            this.physics.removeBody(this.body);
        }

        this.wheelBodies.forEach(wheelBody => {
            this.physics.removeBody(wheelBody);
        });

        this.wheelConstraints.forEach(constraint => {
            this.physics.removeConstraint(constraint);
        });
    }
}
