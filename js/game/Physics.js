/**
 * 物理引擎管理类
 * 基于Cannon-es实现真实的物理模拟
 */

import CANNON from 'cannon-es';

export class Physics {
    constructor() {
        this.world = null;
        this.timeStep = 1 / 60; // 60 FPS
        this.maxSubSteps = 3;
        
        // 物理材质
        this.materials = {
            ground: null,
            car: null,
            wheel: null
        };
        
        // 接触材质
        this.contactMaterials = {};
    }
    
    async init() {
        // 创建物理世界
        this.world = new CANNON.World({
            gravity: new CANNON.Vec3(0, -9.82, 0), // 重力加速度
            broadphase: new CANNON.NaiveBroadphase(), // 碰撞检测算法
            solver: new CANNON.GSSolver() // 约束求解器
        });
        
        // 设置求解器参数
        this.world.solver.iterations = 10;
        this.world.solver.tolerance = 0.1;
        
        // 允许物体休眠以提高性能
        this.world.allowSleep = true;
        
        // 创建物理材质
        this.createMaterials();
        
        // 创建地面
        this.createGround();
        
        console.log('物理引擎初始化完成');
    }
    
    createMaterials() {
        // 地面材质 - 高摩擦力
        this.materials.ground = new CANNON.Material('ground');
        this.materials.ground.friction = 0.8;
        this.materials.ground.restitution = 0.1;
        
        // 车身材质 - 中等摩擦力
        this.materials.car = new CANNON.Material('car');
        this.materials.car.friction = 0.4;
        this.materials.car.restitution = 0.3;
        
        // 轮胎材质 - 高摩擦力，低弹性
        this.materials.wheel = new CANNON.Material('wheel');
        this.materials.wheel.friction = 1.0;
        this.materials.wheel.restitution = 0.1;
        
        // 创建接触材质
        this.contactMaterials.wheelGround = new CANNON.ContactMaterial(
            this.materials.wheel,
            this.materials.ground,
            {
                friction: 0.9,
                restitution: 0.1,
                contactEquationStiffness: 1e8,
                contactEquationRelaxation: 3,
                frictionEquationStiffness: 1e8,
                frictionEquationRelaxation: 3
            }
        );
        
        this.contactMaterials.carGround = new CANNON.ContactMaterial(
            this.materials.car,
            this.materials.ground,
            {
                friction: 0.3,
                restitution: 0.2
            }
        );
        
        // 添加接触材质到世界
        this.world.addContactMaterial(this.contactMaterials.wheelGround);
        this.world.addContactMaterial(this.contactMaterials.carGround);
    }
    
    createGround() {
        // 创建大型地面平面
        const groundShape = new CANNON.Plane();
        const groundBody = new CANNON.Body({
            mass: 0, // 静态物体
            material: this.materials.ground
        });
        groundBody.addShape(groundShape);
        groundBody.quaternion.setFromAxisAngle(new CANNON.Vec3(1, 0, 0), -Math.PI / 2);
        groundBody.position.set(0, 0, 0);
        
        this.world.addBody(groundBody);
    }
    
    // 创建盒子形状的刚体
    createBox(size, position, mass = 1, material = null) {
        const shape = new CANNON.Box(new CANNON.Vec3(size.x / 2, size.y / 2, size.z / 2));
        const body = new CANNON.Body({
            mass: mass,
            material: material || this.materials.car
        });
        body.addShape(shape);
        body.position.set(position.x, position.y, position.z);
        
        this.world.addBody(body);
        return body;
    }
    
    // 创建球形刚体
    createSphere(radius, position, mass = 1, material = null) {
        const shape = new CANNON.Sphere(radius);
        const body = new CANNON.Body({
            mass: mass,
            material: material || this.materials.car
        });
        body.addShape(shape);
        body.position.set(position.x, position.y, position.z);
        
        this.world.addBody(body);
        return body;
    }
    
    // 创建圆柱体刚体（用于轮子）
    createCylinder(radiusTop, radiusBottom, height, position, mass = 1, material = null) {
        const shape = new CANNON.Cylinder(radiusTop, radiusBottom, height, 8);
        const body = new CANNON.Body({
            mass: mass,
            material: material || this.materials.wheel
        });
        body.addShape(shape);
        body.position.set(position.x, position.y, position.z);
        
        this.world.addBody(body);
        return body;
    }
    
    // 创建约束
    createConstraint(bodyA, bodyB, options = {}) {
        let constraint;
        
        switch (options.type) {
            case 'pointToPoint':
                constraint = new CANNON.PointToPointConstraint(
                    bodyA,
                    new CANNON.Vec3(options.pivotA.x, options.pivotA.y, options.pivotA.z),
                    bodyB,
                    new CANNON.Vec3(options.pivotB.x, options.pivotB.y, options.pivotB.z)
                );
                break;
                
            case 'hinge':
                constraint = new CANNON.HingeConstraint(
                    bodyA,
                    bodyB,
                    {
                        pivotA: new CANNON.Vec3(options.pivotA.x, options.pivotA.y, options.pivotA.z),
                        axisA: new CANNON.Vec3(options.axisA.x, options.axisA.y, options.axisA.z),
                        pivotB: new CANNON.Vec3(options.pivotB.x, options.pivotB.y, options.pivotB.z),
                        axisB: new CANNON.Vec3(options.axisB.x, options.axisB.y, options.axisB.z)
                    }
                );
                break;
                
            default:
                console.warn('未知的约束类型:', options.type);
                return null;
        }
        
        this.world.addConstraint(constraint);
        return constraint;
    }
    
    // 添加刚体到物理世界
    addBody(body) {
        this.world.addBody(body);
    }
    
    // 从物理世界移除刚体
    removeBody(body) {
        this.world.removeBody(body);
    }
    
    // 添加约束到物理世界
    addConstraint(constraint) {
        this.world.addConstraint(constraint);
    }
    
    // 从物理世界移除约束
    removeConstraint(constraint) {
        this.world.removeConstraint(constraint);
    }
    
    // 更新物理模拟
    step(deltaTime) {
        // 限制时间步长，避免不稳定
        const clampedDeltaTime = Math.min(deltaTime, 0.1);
        this.world.step(this.timeStep, clampedDeltaTime, this.maxSubSteps);
    }
    
    // 获取物理世界对象
    getWorld() {
        return this.world;
    }
    
    // 获取材质
    getMaterial(name) {
        return this.materials[name];
    }
    
    // 射线检测
    raycast(from, to, options = {}) {
        const result = new CANNON.RaycastResult();
        const ray = new CANNON.Ray(
            new CANNON.Vec3(from.x, from.y, from.z),
            new CANNON.Vec3(to.x, to.y, to.z)
        );
        
        this.world.raycastClosest(ray.from, ray.to, options, result);
        
        return {
            hasHit: result.hasHit,
            body: result.body,
            shape: result.shape,
            hitPoint: result.hitPointWorld,
            hitNormal: result.hitNormalWorld,
            distance: result.distance
        };
    }
    
    // 设置重力
    setGravity(x, y, z) {
        this.world.gravity.set(x, y, z);
    }
    
    // 销毁物理世界
    dispose() {
        if (this.world) {
            // 清理所有刚体
            while (this.world.bodies.length > 0) {
                this.world.removeBody(this.world.bodies[0]);
            }
            
            // 清理所有约束
            while (this.world.constraints.length > 0) {
                this.world.removeConstraint(this.world.constraints[0]);
            }
            
            this.world = null;
        }
    }
}
