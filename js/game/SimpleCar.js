/**
 * 简化赛车类
 * 适配简化物理引擎的赛车实现
 */

import * as THREE from 'three';

export class SimpleCar {
    constructor(scene, physics) {
        this.scene = scene;
        this.physics = physics;
        
        // 3D模型组件
        this.mesh = null;
        this.wheels = [];
        
        // 物理组件
        this.body = null;
        
        // 车辆参数
        this.carSize = { x: 2, y: 0.8, z: 4 };
        this.wheelRadius = 0.4;
        this.wheelWidth = 0.3;
        
        // 控制参数
        this.maxSteerValue = 0.5;
        this.maxForce = 15;
        this.brakeForce = 10;
        this.maxSpeed = 50; // km/h
        
        // 当前状态
        this.currentSpeed = 0;
        this.currentSteer = 0;
        this.velocity = { x: 0, y: 0, z: 0 };
        this.position = { x: 0, y: 2, z: -90 };
        this.rotation = { x: 0, y: 0, z: 0 };
    }
    
    async create() {
        // 创建车身
        this.createCarBody();
        
        // 创建轮子
        this.createWheels();
        
        // 创建物理体
        this.body = this.physics.createBox(
            this.carSize,
            this.position,
            500, // 车身质量
            this.physics.getMaterial('car')
        );
        
        // 设置初始位置
        this.reset();
        
        console.log('简化赛车创建完成');
    }
    
    createCarBody() {
        // 创建车身几何体
        const carGroup = new THREE.Group();
        
        // 主车身
        const bodyGeometry = new THREE.BoxGeometry(
            this.carSize.x,
            this.carSize.y,
            this.carSize.z
        );
        
        const bodyMaterial = new THREE.MeshLambertMaterial({
            color: 0x333333,
            transparent: false
        });
        
        const bodyMesh = new THREE.Mesh(bodyGeometry, bodyMaterial);
        bodyMesh.castShadow = true;
        bodyMesh.receiveShadow = true;
        carGroup.add(bodyMesh);
        
        // 车顶
        const roofGeometry = new THREE.BoxGeometry(
            this.carSize.x * 0.8,
            this.carSize.y * 0.4,
            this.carSize.z * 0.6
        );
        
        const roofMaterial = new THREE.MeshLambertMaterial({
            color: 0x666666,
            transparent: false
        });
        
        const roofMesh = new THREE.Mesh(roofGeometry, roofMaterial);
        roofMesh.position.y = this.carSize.y * 0.6;
        roofMesh.castShadow = true;
        carGroup.add(roofMesh);
        
        // 前保险杠
        const bumperGeometry = new THREE.BoxGeometry(
            this.carSize.x * 0.9,
            this.carSize.y * 0.3,
            0.2
        );
        
        const bumperMaterial = new THREE.MeshLambertMaterial({
            color: 0xffffff,
            transparent: false
        });
        
        const frontBumper = new THREE.Mesh(bumperGeometry, bumperMaterial);
        frontBumper.position.z = this.carSize.z / 2 + 0.1;
        frontBumper.position.y = -this.carSize.y * 0.2;
        frontBumper.castShadow = true;
        carGroup.add(frontBumper);
        
        // 后保险杠
        const rearBumper = new THREE.Mesh(bumperGeometry, bumperMaterial);
        rearBumper.position.z = -this.carSize.z / 2 - 0.1;
        rearBumper.position.y = -this.carSize.y * 0.2;
        rearBumper.castShadow = true;
        carGroup.add(rearBumper);
        
        // 添加车灯
        this.addLights(carGroup);
        
        this.mesh = carGroup;
        this.scene.add(this.mesh);
    }
    
    addLights(carGroup) {
        // 前大灯
        const headlightGeometry = new THREE.SphereGeometry(0.15, 8, 6);
        const headlightMaterial = new THREE.MeshLambertMaterial({
            color: 0xffffcc,
            emissive: 0x444422
        });
        
        const leftHeadlight = new THREE.Mesh(headlightGeometry, headlightMaterial);
        leftHeadlight.position.set(-0.6, 0, this.carSize.z / 2 - 0.2);
        carGroup.add(leftHeadlight);
        
        const rightHeadlight = new THREE.Mesh(headlightGeometry, headlightMaterial);
        rightHeadlight.position.set(0.6, 0, this.carSize.z / 2 - 0.2);
        carGroup.add(rightHeadlight);
        
        // 尾灯
        const taillightGeometry = new THREE.SphereGeometry(0.1, 8, 6);
        const taillightMaterial = new THREE.MeshLambertMaterial({
            color: 0xff4444,
            emissive: 0x220000
        });
        
        const leftTaillight = new THREE.Mesh(taillightGeometry, taillightMaterial);
        leftTaillight.position.set(-0.6, 0, -this.carSize.z / 2 + 0.2);
        carGroup.add(leftTaillight);
        
        const rightTaillight = new THREE.Mesh(taillightGeometry, taillightMaterial);
        rightTaillight.position.set(0.6, 0, -this.carSize.z / 2 + 0.2);
        carGroup.add(rightTaillight);
    }
    
    createWheels() {
        const wheelPositions = [
            { x: -1.2, y: -0.2, z: 1.5 },   // 左前
            { x: 1.2, y: -0.2, z: 1.5 },    // 右前
            { x: -1.2, y: -0.2, z: -1.5 },  // 左后
            { x: 1.2, y: -0.2, z: -1.5 }    // 右后
        ];
        
        wheelPositions.forEach((pos, index) => {
            // 创建轮子3D模型
            const wheelGeometry = new THREE.CylinderGeometry(
                this.wheelRadius,
                this.wheelRadius,
                this.wheelWidth,
                16
            );
            
            const wheelMaterial = new THREE.MeshLambertMaterial({
                color: 0x222222,
                transparent: false
            });
            
            const wheelMesh = new THREE.Mesh(wheelGeometry, wheelMaterial);
            wheelMesh.rotation.z = Math.PI / 2; // 旋转轮子方向
            wheelMesh.castShadow = true;
            wheelMesh.receiveShadow = true;
            
            // 添加轮毂
            const rimGeometry = new THREE.CylinderGeometry(
                this.wheelRadius * 0.7,
                this.wheelRadius * 0.7,
                this.wheelWidth * 0.8,
                8
            );
            
            const rimMaterial = new THREE.MeshLambertMaterial({
                color: 0xcccccc,
                transparent: false
            });
            
            const rimMesh = new THREE.Mesh(rimGeometry, rimMaterial);
            rimMesh.rotation.z = Math.PI / 2;
            wheelMesh.add(rimMesh);
            
            this.wheels.push({
                mesh: wheelMesh,
                offset: pos,
                steerAngle: 0
            });
            this.scene.add(wheelMesh);
        });
    }
    
    update(deltaTime, keys) {
        // 更新物理状态
        this.updatePhysics(deltaTime, keys);
        
        // 同步3D模型位置
        this.syncMeshWithPhysics();
        
        // 更新速度
        this.updateSpeed();
    }
    
    updatePhysics(deltaTime, keys) {
        // 转向控制
        let steerInput = 0;
        if (keys.left) steerInput += 1;
        if (keys.right) steerInput -= 1;
        
        // 平滑转向
        const steerSpeed = 3.0;
        this.currentSteer = THREE.MathUtils.lerp(
            this.currentSteer,
            steerInput * this.maxSteerValue,
            steerSpeed * deltaTime
        );
        
        // 加速和刹车
        let forceInput = 0;
        if (keys.forward) {
            forceInput = 1;
        } else if (keys.backward) {
            forceInput = -0.5; // 倒车力度较小
        }
        
        // 手刹
        const isBraking = keys.brake;
        
        // 计算前进方向（修正朝向）
        const forward = {
            x: -Math.sin(this.rotation.y),
            z: -Math.cos(this.rotation.y)
        };
        
        // 应用驱动力
        if (Math.abs(forceInput) > 0) {
            const force = forceInput * this.maxForce;
            this.physics.applyForce(this.body, {
                x: forward.x * force,
                y: 0,
                z: forward.z * force
            });
        }
        
        // 应用刹车力
        if (isBraking) {
            this.physics.applyForce(this.body, {
                x: -this.body.velocity.x * this.brakeForce,
                y: 0,
                z: -this.body.velocity.z * this.brakeForce
            });
        }
        
        // 转向（只有在移动时才有效）
        const speed = Math.sqrt(this.body.velocity.x * this.body.velocity.x + this.body.velocity.z * this.body.velocity.z);
        if (speed > 0.1 && Math.abs(this.currentSteer) > 0.01) {
            this.rotation.y += this.currentSteer * speed * deltaTime * 0.5;
        }
        
        // 更新位置和速度
        this.position.x = this.body.position.x;
        this.position.y = this.body.position.y;
        this.position.z = this.body.position.z;
        
        this.velocity.x = this.body.velocity.x;
        this.velocity.y = this.body.velocity.y;
        this.velocity.z = this.body.velocity.z;
    }
    
    syncMeshWithPhysics() {
        // 同步车身位置和旋转
        this.mesh.position.set(this.position.x, this.position.y, this.position.z);
        this.mesh.rotation.y = this.rotation.y;
        
        // 同步轮子位置
        this.wheels.forEach((wheel, index) => {
            const isFrontWheel = index < 2;
            
            // 计算轮子的世界位置
            const offset = wheel.offset;
            const cos = Math.cos(this.rotation.y);
            const sin = Math.sin(this.rotation.y);
            
            const worldX = this.position.x + (offset.x * cos - offset.z * sin);
            const worldZ = this.position.z + (offset.x * sin + offset.z * cos);
            
            wheel.mesh.position.set(worldX, this.position.y + offset.y, worldZ);
            
            // 前轮转向
            if (isFrontWheel) {
                wheel.mesh.rotation.y = this.rotation.y + this.currentSteer;
            } else {
                wheel.mesh.rotation.y = this.rotation.y;
            }
        });
    }
    
    updateSpeed() {
        const speedMs = Math.sqrt(this.velocity.x * this.velocity.x + this.velocity.z * this.velocity.z);
        this.currentSpeed = speedMs * 3.6; // 转换为 km/h
    }
    
    getSpeed() {
        return Math.round(this.currentSpeed);
    }
    
    getPosition() {
        return this.position;
    }
    
    reset() {
        // 重置到起始位置
        const startPos = { x: 0, y: 2, z: -90 };

        // 重置车身（车头朝向赛道前方）
        this.position = { ...startPos };
        this.velocity = { x: 0, y: 0, z: 0 };
        this.rotation = { x: 0, y: Math.PI, z: 0 }; // 旋转180度，让车头朝向正确方向
        
        // 更新物理体
        this.body.position.x = startPos.x;
        this.body.position.y = startPos.y;
        this.body.position.z = startPos.z;
        this.body.velocity.x = 0;
        this.body.velocity.y = 0;
        this.body.velocity.z = 0;
        
        // 重置状态
        this.currentSpeed = 0;
        this.currentSteer = 0;
    }
    
    dispose() {
        // 清理3D模型
        if (this.mesh) {
            this.scene.remove(this.mesh);
        }
        
        this.wheels.forEach(wheel => {
            this.scene.remove(wheel.mesh);
            wheel.mesh.geometry.dispose();
            wheel.mesh.material.dispose();
        });
        
        // 清理物理体
        if (this.body) {
            this.physics.removeBody(this.body);
        }
    }
}
