/**
 * 赛道类
 * 创建黑白美学风格的3D赛道
 */

import * as THREE from 'three';

export class Track {
    constructor(scene, physics) {
        this.scene = scene;
        this.physics = physics;
        this.trackMesh = null;
        this.barriers = [];
        this.decorations = [];
        
        // 赛道参数
        this.trackWidth = 12;
        this.trackLength = 200;
        this.barrierHeight = 2;
        this.barrierWidth = 0.5;
    }
    
    async create() {
        // 创建主赛道
        this.createMainTrack();
        
        // 创建赛道边界
        this.createTrackBarriers();
        
        // 添加装饰元素
        this.createDecorations();
        
        // 添加起跑线
        this.createStartLine();
        
        console.log('赛道创建完成');
    }
    
    createMainTrack() {
        // 创建赛道几何体
        const trackGeometry = new THREE.PlaneGeometry(this.trackWidth, this.trackLength);
        
        // 创建赛道材质 - 黑白条纹效果
        const canvas = document.createElement('canvas');
        canvas.width = 512;
        canvas.height = 512;
        const ctx = canvas.getContext('2d');
        
        // 绘制赛道纹理
        ctx.fillStyle = '#1a1a1a';
        ctx.fillRect(0, 0, 512, 512);
        
        // 添加中央分隔线
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(240, 0, 32, 512);
        
        // 添加边线
        ctx.fillRect(0, 0, 16, 512);
        ctx.fillRect(496, 0, 16, 512);
        
        // 添加虚线效果
        for (let i = 0; i < 512; i += 64) {
            ctx.fillRect(240, i, 32, 24);
        }
        
        const trackTexture = new THREE.CanvasTexture(canvas);
        trackTexture.wrapS = THREE.RepeatWrapping;
        trackTexture.wrapT = THREE.RepeatWrapping;
        trackTexture.repeat.set(1, 8);
        
        const trackMaterial = new THREE.MeshLambertMaterial({
            map: trackTexture,
            transparent: false
        });
        
        // 创建赛道网格
        this.trackMesh = new THREE.Mesh(trackGeometry, trackMaterial);
        this.trackMesh.rotation.x = -Math.PI / 2;
        this.trackMesh.position.y = 0.01; // 稍微抬高避免z-fighting
        this.trackMesh.receiveShadow = true;
        
        this.scene.add(this.trackMesh);
    }
    
    createTrackBarriers() {
        // 左侧护栏
        this.createBarrier(-this.trackWidth / 2 - this.barrierWidth / 2, 0);
        
        // 右侧护栏
        this.createBarrier(this.trackWidth / 2 + this.barrierWidth / 2, 0);
    }
    
    createBarrier(x, z) {
        const segments = Math.floor(this.trackLength / 4); // 每4米一段护栏
        
        for (let i = 0; i < segments; i++) {
            const segmentZ = -this.trackLength / 2 + (i * 4) + 2;
            
            // 创建护栏几何体
            const barrierGeometry = new THREE.BoxGeometry(
                this.barrierWidth,
                this.barrierHeight,
                3.5 // 留0.5米间隙
            );
            
            // 护栏材质 - 纯白色
            const barrierMaterial = new THREE.MeshLambertMaterial({
                color: 0xffffff,
                transparent: false
            });
            
            const barrierMesh = new THREE.Mesh(barrierGeometry, barrierMaterial);
            barrierMesh.position.set(x, this.barrierHeight / 2, segmentZ);
            barrierMesh.castShadow = true;
            barrierMesh.receiveShadow = true;
            
            this.scene.add(barrierMesh);
            this.barriers.push(barrierMesh);
            
            // 创建对应的物理刚体
            const barrierBody = this.physics.createBox(
                { x: this.barrierWidth, y: this.barrierHeight, z: 3.5 },
                { x: x, y: this.barrierHeight / 2, z: segmentZ },
                0, // 静态物体
                this.physics.getMaterial('ground')
            );
        }
    }
    
    createDecorations() {
        // 创建路边装饰 - 简约的几何体
        this.createRoadSideDecorations();
        
        // 创建远景装饰
        this.createBackgroundDecorations();
    }
    
    createRoadSideDecorations() {
        const decorationPositions = [
            { x: -20, z: -80 },
            { x: 20, z: -60 },
            { x: -25, z: -40 },
            { x: 25, z: -20 },
            { x: -20, z: 0 },
            { x: 20, z: 20 },
            { x: -25, z: 40 },
            { x: 25, z: 60 },
            { x: -20, z: 80 }
        ];
        
        decorationPositions.forEach((pos, index) => {
            // 创建简约的立方体装饰
            const size = 2 + Math.random() * 3;
            const height = 1 + Math.random() * 4;
            
            const decorationGeometry = new THREE.BoxGeometry(size, height, size);
            const decorationMaterial = new THREE.MeshLambertMaterial({
                color: index % 2 === 0 ? 0x333333 : 0x666666,
                transparent: false
            });
            
            const decorationMesh = new THREE.Mesh(decorationGeometry, decorationMaterial);
            decorationMesh.position.set(pos.x, height / 2, pos.z);
            decorationMesh.castShadow = true;
            decorationMesh.receiveShadow = true;
            
            this.scene.add(decorationMesh);
            this.decorations.push(decorationMesh);
        });
    }
    
    createBackgroundDecorations() {
        // 创建远景的抽象几何体
        for (let i = 0; i < 20; i++) {
            const distance = 50 + Math.random() * 100;
            const angle = Math.random() * Math.PI * 2;
            const x = Math.cos(angle) * distance;
            const z = Math.sin(angle) * distance;
            const height = 5 + Math.random() * 15;
            
            const bgGeometry = new THREE.CylinderGeometry(
                0.5 + Math.random() * 2,
                1 + Math.random() * 3,
                height,
                6
            );
            
            const bgMaterial = new THREE.MeshLambertMaterial({
                color: new THREE.Color().setHSL(0, 0, 0.1 + Math.random() * 0.3),
                transparent: true,
                opacity: 0.6
            });
            
            const bgMesh = new THREE.Mesh(bgGeometry, bgMaterial);
            bgMesh.position.set(x, height / 2, z);
            bgMesh.castShadow = true;
            
            this.scene.add(bgMesh);
            this.decorations.push(bgMesh);
        }
    }
    
    createStartLine() {
        // 创建起跑线
        const startLineGeometry = new THREE.PlaneGeometry(this.trackWidth, 2);
        
        // 创建黑白格子纹理
        const canvas = document.createElement('canvas');
        canvas.width = 128;
        canvas.height = 128;
        const ctx = canvas.getContext('2d');
        
        const checkSize = 16;
        for (let x = 0; x < 128; x += checkSize) {
            for (let y = 0; y < 128; y += checkSize) {
                const isWhite = ((x / checkSize) + (y / checkSize)) % 2 === 0;
                ctx.fillStyle = isWhite ? '#ffffff' : '#000000';
                ctx.fillRect(x, y, checkSize, checkSize);
            }
        }
        
        const startLineTexture = new THREE.CanvasTexture(canvas);
        const startLineMaterial = new THREE.MeshLambertMaterial({
            map: startLineTexture,
            transparent: false
        });
        
        const startLineMesh = new THREE.Mesh(startLineGeometry, startLineMaterial);
        startLineMesh.rotation.x = -Math.PI / 2;
        startLineMesh.position.set(0, 0.02, -this.trackLength / 2 + 10);
        startLineMesh.receiveShadow = true;
        
        this.scene.add(startLineMesh);
    }
    
    // 获取赛道边界信息
    getTrackBounds() {
        return {
            width: this.trackWidth,
            length: this.trackLength,
            leftBound: -this.trackWidth / 2,
            rightBound: this.trackWidth / 2,
            frontBound: -this.trackLength / 2,
            backBound: this.trackLength / 2
        };
    }
    
    // 检查位置是否在赛道内
    isOnTrack(position) {
        const bounds = this.getTrackBounds();
        return position.x >= bounds.leftBound &&
               position.x <= bounds.rightBound &&
               position.z >= bounds.frontBound &&
               position.z <= bounds.backBound;
    }
    
    // 获取起始位置
    getStartPosition() {
        return {
            x: 0,
            y: 1,
            z: -this.trackLength / 2 + 5
        };
    }
    
    // 销毁赛道
    dispose() {
        // 清理网格
        if (this.trackMesh) {
            this.scene.remove(this.trackMesh);
            this.trackMesh.geometry.dispose();
            this.trackMesh.material.dispose();
        }
        
        // 清理护栏
        this.barriers.forEach(barrier => {
            this.scene.remove(barrier);
            barrier.geometry.dispose();
            barrier.material.dispose();
        });
        
        // 清理装饰
        this.decorations.forEach(decoration => {
            this.scene.remove(decoration);
            decoration.geometry.dispose();
            decoration.material.dispose();
        });
        
        this.barriers = [];
        this.decorations = [];
    }
}
