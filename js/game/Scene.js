/**
 * 3D场景管理类
 * 负责Three.js场景的创建、渲染和管理
 */

import * as THREE from 'three';

export class GameScene {
    constructor(canvas) {
        this.canvas = canvas;
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.cameraTarget = null;
        this.cameraOffset = new THREE.Vector3(0, 8, 15);
        this.cameraLookOffset = new THREE.Vector3(0, 0, -5);
        
        // 相机跟随参数
        this.cameraLerpFactor = 0.05;
        this.cameraLookLerpFactor = 0.1;
    }
    
    async init() {
        // 创建场景
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x000000);
        this.scene.fog = new THREE.Fog(0x000000, 50, 200);
        
        // 创建相机
        this.camera = new THREE.PerspectiveCamera(
            75,
            window.innerWidth / window.innerHeight,
            0.1,
            1000
        );
        this.camera.position.set(0, 10, 20);
        
        // 创建渲染器
        this.renderer = new THREE.WebGLRenderer({
            canvas: this.canvas,
            antialias: true,
            alpha: false
        });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.renderer.outputColorSpace = THREE.SRGBColorSpace;
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 1.0;
        
        // 设置光照
        this.setupLighting();
        
        // 添加网格地面（调试用）
        this.addGridGround();
        
        console.log('3D场景初始化完成');
    }
    
    setupLighting() {
        // 环境光 - 提供基础照明
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.4);
        this.scene.add(ambientLight);
        
        // 主方向光 - 模拟太阳光
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(50, 100, 50);
        directionalLight.castShadow = true;
        
        // 优化阴影质量
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 500;
        directionalLight.shadow.camera.left = -100;
        directionalLight.shadow.camera.right = 100;
        directionalLight.shadow.camera.top = 100;
        directionalLight.shadow.camera.bottom = -100;
        directionalLight.shadow.bias = -0.0001;
        
        this.scene.add(directionalLight);
        
        // 补充光源 - 减少阴影过暗
        const fillLight = new THREE.DirectionalLight(0xffffff, 0.3);
        fillLight.position.set(-50, 50, -50);
        this.scene.add(fillLight);
        
        // 边缘光 - 增强立体感
        const rimLight = new THREE.DirectionalLight(0xffffff, 0.2);
        rimLight.position.set(0, 10, -100);
        this.scene.add(rimLight);
    }
    
    addGridGround() {
        // 创建网格地面，体现黑白美学
        const gridSize = 200;
        const gridDivisions = 100;
        
        const gridHelper = new THREE.GridHelper(
            gridSize, 
            gridDivisions, 
            0x333333, 
            0x111111
        );
        gridHelper.position.y = -0.01; // 稍微下沉避免z-fighting
        this.scene.add(gridHelper);
        
        // 添加地面平面用于接收阴影
        const groundGeometry = new THREE.PlaneGeometry(gridSize, gridSize);
        const groundMaterial = new THREE.MeshLambertMaterial({ 
            color: 0x000000,
            transparent: true,
            opacity: 0.8
        });
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.receiveShadow = true;
        this.scene.add(ground);
    }
    
    setCameraTarget(target) {
        this.cameraTarget = target;
    }
    
    update(deltaTime) {
        if (this.cameraTarget) {
            this.updateCamera(deltaTime);
        }
    }
    
    updateCamera(deltaTime) {
        // 获取目标位置
        const targetPosition = this.cameraTarget.position.clone();
        const targetRotation = this.cameraTarget.rotation;
        
        // 计算相机应该在的位置（相对于车辆）
        const offset = this.cameraOffset.clone();
        offset.applyEuler(new THREE.Euler(0, targetRotation.y, 0));
        const desiredCameraPosition = targetPosition.clone().add(offset);
        
        // 计算相机应该看向的位置
        const lookOffset = this.cameraLookOffset.clone();
        lookOffset.applyEuler(new THREE.Euler(0, targetRotation.y, 0));
        const desiredLookPosition = targetPosition.clone().add(lookOffset);
        
        // 平滑移动相机位置
        this.camera.position.lerp(desiredCameraPosition, this.cameraLerpFactor);
        
        // 平滑调整相机朝向
        const currentLookTarget = new THREE.Vector3();
        this.camera.getWorldDirection(currentLookTarget);
        currentLookTarget.multiplyScalar(10).add(this.camera.position);
        
        currentLookTarget.lerp(desiredLookPosition, this.cameraLookLerpFactor);
        this.camera.lookAt(currentLookTarget);
    }
    
    render() {
        this.renderer.render(this.scene, this.camera);
    }
    
    onWindowResize() {
        // 更新相机宽高比
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        
        // 更新渲染器尺寸
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    }
    
    // 添加对象到场景
    add(object) {
        this.scene.add(object);
    }
    
    // 从场景移除对象
    remove(object) {
        this.scene.remove(object);
    }
    
    // 获取场景对象（供其他模块使用）
    getScene() {
        return this.scene;
    }
    
    // 获取相机对象
    getCamera() {
        return this.camera;
    }
    
    // 获取渲染器对象
    getRenderer() {
        return this.renderer;
    }
    
    // 设置相机参数
    setCameraOffset(x, y, z) {
        this.cameraOffset.set(x, y, z);
    }
    
    setCameraLookOffset(x, y, z) {
        this.cameraLookOffset.set(x, y, z);
    }
    
    setCameraLerpFactor(factor) {
        this.cameraLerpFactor = Math.max(0.01, Math.min(1.0, factor));
    }
    
    // 销毁资源
    dispose() {
        if (this.renderer) {
            this.renderer.dispose();
        }
        
        // 清理场景中的所有对象
        while (this.scene.children.length > 0) {
            const child = this.scene.children[0];
            this.scene.remove(child);
            
            if (child.geometry) child.geometry.dispose();
            if (child.material) {
                if (Array.isArray(child.material)) {
                    child.material.forEach(material => material.dispose());
                } else {
                    child.material.dispose();
                }
            }
        }
    }
}
