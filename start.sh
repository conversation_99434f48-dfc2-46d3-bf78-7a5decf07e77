#!/bin/bash

# 3D赛车小游戏启动脚本

echo "🏎️  3D赛车小游戏"
echo "=================="
echo ""
echo "正在启动本地服务器..."
echo ""

# 检查Python是否可用
if command -v python3 &> /dev/null; then
    echo "使用 Python3 启动服务器..."
    echo "服务器地址: http://localhost:8080"
    echo ""
    echo "按 Ctrl+C 停止服务器"
    echo ""
    python3 -m http.server 8080
elif command -v python &> /dev/null; then
    echo "使用 Python 启动服务器..."
    echo "服务器地址: http://localhost:8080"
    echo ""
    echo "按 Ctrl+C 停止服务器"
    echo ""
    python -m http.server 8080
else
    echo "❌ 错误: 未找到 Python"
    echo ""
    echo "请安装 Python 或使用其他方式启动本地服务器："
    echo "- 使用 Live Server (VS Code 扩展)"
    echo "- 使用 Node.js: npx serve ."
    echo "- 使用其他本地服务器工具"
    echo ""
    exit 1
fi
