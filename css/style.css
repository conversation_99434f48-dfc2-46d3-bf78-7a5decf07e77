/* 3D赛车游戏样式 - 纯粹黑白美学 */

/* 重置与基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'JetBrains Mono', 'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', monospace;
    background: #000000;
    color: #ffffff;
    overflow: hidden;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

/* 游戏容器 */
#game-container {
    position: relative;
    width: 100vw;
    height: 100vh;
    background: #000000;
}

/* 3D画布 */
#game-canvas {
    display: block;
    width: 100%;
    height: 100%;
    background: #000000;
    cursor: none;
    outline: none; /* 移除焦点时的边框 */
}

#game-canvas:focus {
    outline: none;
}

/* UI覆盖层 */
#game-ui {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 100;
}

/* 顶部信息栏 */
.ui-top {
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    pointer-events: auto;
}

.info-panel {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid #ffffff;
    padding: 15px 20px;
    backdrop-filter: blur(10px);
}

.speed-display,
.time-display {
    display: flex;
    align-items: baseline;
    gap: 8px;
}

.label {
    font-size: 12px;
    font-weight: 300;
    opacity: 0.7;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.value {
    font-size: 24px;
    font-weight: 700;
    font-variant-numeric: tabular-nums;
}

.unit {
    font-size: 14px;
    font-weight: 400;
    opacity: 0.8;
}

/* 底部控制提示 */
.ui-bottom {
    position: absolute;
    bottom: 20px;
    left: 20px;
    right: 20px;
    pointer-events: auto;
}

.controls-hint {
    display: flex;
    justify-content: center;
    gap: 30px;
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid #ffffff;
    padding: 15px 20px;
    backdrop-filter: blur(10px);
}

.control-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.key {
    font-size: 12px;
    font-weight: 700;
    padding: 4px 8px;
    border: 1px solid #ffffff;
    background: #ffffff;
    color: #000000;
    min-width: 30px;
    text-align: center;
}

.action {
    font-size: 10px;
    font-weight: 300;
    opacity: 0.7;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 游戏界面屏幕 */
.screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    pointer-events: auto;
    backdrop-filter: blur(20px);
}

.screen.hidden {
    display: none;
}

.screen-content {
    text-align: center;
    max-width: 500px;
    padding: 40px;
    border: 1px solid #ffffff;
    background: rgba(0, 0, 0, 0.9);
}

/* 游戏标题 */
.game-title {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 10px;
    letter-spacing: 2px;
    text-transform: uppercase;
}

.game-subtitle {
    font-size: 14px;
    font-weight: 300;
    opacity: 0.7;
    margin-bottom: 40px;
    letter-spacing: 1px;
}

.screen-title {
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 30px;
    letter-spacing: 1px;
    text-transform: uppercase;
}

/* 游戏按钮 */
.game-button {
    font-family: inherit;
    font-size: 16px;
    font-weight: 500;
    padding: 15px 30px;
    margin: 10px;
    background: #ffffff;
    color: #000000;
    border: 1px solid #ffffff;
    cursor: pointer;
    transition: all 0.2s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    min-width: 150px;
}

.game-button:hover {
    background: #000000;
    color: #ffffff;
    transform: translateY(-2px);
}

.game-button:active {
    transform: translateY(0);
}

/* 游戏信息 */
.game-info {
    margin-top: 30px;
    font-size: 12px;
    font-weight: 300;
    opacity: 0.6;
    line-height: 1.6;
}

.game-info p {
    margin: 5px 0;
}

/* 加载动画 */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 14px;
    font-weight: 300;
    opacity: 0.7;
    letter-spacing: 1px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .info-panel {
        padding: 10px 15px;
        font-size: 14px;
    }
    
    .value {
        font-size: 20px;
    }
    
    .controls-hint {
        gap: 15px;
        padding: 10px 15px;
    }
    
    .control-group {
        gap: 3px;
    }
    
    .key {
        font-size: 10px;
        padding: 3px 6px;
        min-width: 25px;
    }
    
    .action {
        font-size: 8px;
    }
    
    .game-title {
        font-size: 36px;
    }
    
    .screen-content {
        padding: 30px 20px;
        margin: 20px;
    }
}

@media (max-width: 480px) {
    .ui-top,
    .ui-bottom {
        left: 10px;
        right: 10px;
    }
    
    .controls-hint {
        gap: 10px;
        flex-wrap: wrap;
    }
    
    .game-title {
        font-size: 28px;
    }
    
    .game-button {
        padding: 12px 20px;
        font-size: 14px;
        min-width: 120px;
    }
}
