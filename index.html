<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D赛车小游戏</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 游戏容器 -->
    <div id="game-container">
        <!-- 3D渲染画布 -->
        <canvas id="game-canvas"></canvas>
        
        <!-- 游戏UI覆盖层 -->
        <div id="game-ui">
            <!-- 顶部信息栏 -->
            <div class="ui-top">
                <div class="info-panel">
                    <div class="speed-display">
                        <span class="label">速度</span>
                        <span class="value" id="speed-value">0</span>
                        <span class="unit">km/h</span>
                    </div>
                    <div class="time-display">
                        <span class="label">时间</span>
                        <span class="value" id="time-value">00:00</span>
                    </div>
                </div>
            </div>
            
            <!-- 底部控制提示 -->
            <div class="ui-bottom">
                <div class="controls-hint">
                    <div class="control-group">
                        <span class="key">W/↑</span>
                        <span class="action">加速</span>
                    </div>
                    <div class="control-group">
                        <span class="key">S/↓</span>
                        <span class="action">刹车</span>
                    </div>
                    <div class="control-group">
                        <span class="key">A/←</span>
                        <span class="action">左转</span>
                    </div>
                    <div class="control-group">
                        <span class="key">D/→</span>
                        <span class="action">右转</span>
                    </div>
                    <div class="control-group">
                        <span class="key">空格</span>
                        <span class="action">手刹</span>
                    </div>
                </div>
            </div>
            
            <!-- 开始界面 -->
            <div id="start-screen" class="screen">
                <div class="screen-content">
                    <h1 class="game-title">3D赛车</h1>
                    <p class="game-subtitle">纯粹黑白美学 · 极致驾驶体验</p>
                    <button id="start-button" class="game-button">开始游戏</button>
                    <div class="game-info">
                        <p>使用WASD或方向键控制赛车</p>
                        <p>空格键为手刹</p>
                    </div>
                </div>
            </div>
            
            <!-- 暂停界面 -->
            <div id="pause-screen" class="screen hidden">
                <div class="screen-content">
                    <h2 class="screen-title">游戏暂停</h2>
                    <button id="resume-button" class="game-button">继续游戏</button>
                    <button id="restart-button" class="game-button">重新开始</button>
                </div>
            </div>
            
            <!-- 加载界面 -->
            <div id="loading-screen" class="screen hidden">
                <div class="screen-content">
                    <div class="loading-spinner"></div>
                    <p class="loading-text">加载中...</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 音频元素 - 使用合成音效，这些元素仅作为占位符 -->
    <audio id="engine-sound" loop preload="none" style="display: none;">
    </audio>

    <audio id="brake-sound" preload="none" style="display: none;">
    </audio>

    <audio id="collision-sound" preload="none" style="display: none;">
    </audio>
    
    <!-- 脚本加载 -->
    <script type="importmap">
    {
        "imports": {
            "three": "https://unpkg.com/three@0.158.0/build/three.module.js",
            "three/addons/": "https://unpkg.com/three@0.158.0/examples/jsm/",
            "cannon-es": "https://unpkg.com/cannon-es@0.20.0/dist/cannon-es.js"
        }
    }
    </script>
    
    <script type="module" src="js/main.js"></script>
</body>
</html>
